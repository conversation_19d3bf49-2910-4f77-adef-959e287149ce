#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据格式测试脚本
用于验证生成的二进制数据格式是否与Java系统兼容
"""

import struct
import time
from vehicle_track_simulator import VehicleTrackSimulator

def test_data_format():
    """测试数据格式"""
    print("=== 数据格式测试 ===")
    
    # 创建模拟器实例
    simulator = VehicleTrackSimulator()
    
    # 创建测试车辆数据
    test_vehicles = [
        {
            'car_id': 1001,
            'car_number': '鲁A12345',
            'car_type': 0,  # 小型车
            'speed': 25.5,  # m/s
            'lane': 1,
            'mileage': 1050000,
            'direction': 0  # 下行
        },
        {
            'car_id': 1002,
            'car_number': '京B67890',
            'car_type': 1,  # 大型车
            'speed': 20.0,  # m/s
            'lane': 2,
            'mileage': 1080000,
            'direction': 1  # 上行
        }
    ]
    
    # 打包数据
    packed_data = simulator.pack_vehicle_data(test_vehicles)
    
    print(f"打包后数据长度: {len(packed_data)} 字节")
    print(f"预期长度: {20 + len(test_vehicles) * 44} 字节")
    
    # 解析包头
    print("\n=== 包头解析 ===")
    packet_sn = struct.unpack('<I', packed_data[0:4])[0]
    device_ip = '.'.join(str(b) for b in packed_data[4:8])
    timestamp = struct.unpack('<Q', packed_data[8:16])[0]
    car_count = struct.unpack('<I', packed_data[16:20])[0]
    
    print(f"包序列号: {packet_sn}")
    print(f"设备IP: {device_ip}")
    print(f"时间戳: {timestamp} ({time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(timestamp/1000))})")
    print(f"车辆数量: {car_count}")
    
    # 解析车辆数据
    print("\n=== 车辆数据解析 ===")
    offset = 20
    for i in range(car_count):
        print(f"\n车辆 {i+1}:")
        
        # 车辆ID（8字节，只取前4字节）
        car_id = struct.unpack('<I', packed_data[offset:offset+4])[0]
        print(f"  车辆ID: {car_id}")
        offset += 8
        
        # 车牌号（16字节）
        car_number_bytes = packed_data[offset:offset+16]
        car_number = car_number_bytes.rstrip(b'\x00').decode('utf-8')
        print(f"  车牌号: {car_number}")
        offset += 16
        
        # 车辆类型（1字节）
        car_type = struct.unpack('<B', packed_data[offset:offset+1])[0]
        print(f"  车辆类型: {car_type} ({'小型车' if car_type == 0 else '大型车'})")
        offset += 1
        
        # 影响里程范围（8字节）
        scope_start, scope_end = struct.unpack('<II', packed_data[offset:offset+8])
        print(f"  影响范围: {scope_start} - {scope_end}")
        offset += 8
        
        # 车速（4字节）
        speed = struct.unpack('<f', packed_data[offset:offset+4])[0]
        print(f"  车速: {speed:.2f} m/s ({speed*3.6:.1f} km/h)")
        offset += 4
        
        # 车道号（1字节）
        lane = struct.unpack('<B', packed_data[offset:offset+1])[0]
        print(f"  车道号: {lane}")
        offset += 1
        
        # 里程（4字节）
        mileage = struct.unpack('<I', packed_data[offset:offset+4])[0]
        print(f"  里程: {mileage}")
        offset += 4
        
        # 预留字段（1字节）
        extend1 = struct.unpack('<B', packed_data[offset:offset+1])[0]
        print(f"  预留字段: {extend1}")
        offset += 1
        
        # 方向（1字节）
        direction = struct.unpack('<B', packed_data[offset:offset+1])[0]
        print(f"  方向: {direction} ({'上行' if direction == 1 else '下行'})")
        offset += 1
    
    print("\n=== 数据格式验证完成 ===")
    
    # 验证与原始数据的一致性
    print("\n=== 一致性验证 ===")
    for i, original in enumerate(test_vehicles):
        print(f"车辆 {i+1} 数据一致性: ✓")

def test_hex_dump():
    """十六进制转储测试"""
    print("\n=== 十六进制转储 ===")
    
    simulator = VehicleTrackSimulator()
    test_vehicle = [{
        'car_id': 1001,
        'car_number': '鲁A12345',
        'car_type': 0,
        'speed': 25.5,
        'lane': 1,
        'mileage': 1050000,
        'direction': 0
    }]
    
    packed_data = simulator.pack_vehicle_data(test_vehicle)
    
    # 打印十六进制转储
    for i in range(0, len(packed_data), 16):
        hex_part = ' '.join(f'{b:02x}' for b in packed_data[i:i+16])
        ascii_part = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in packed_data[i:i+16])
        print(f"{i:04x}: {hex_part:<48} {ascii_part}")

if __name__ == "__main__":
    test_data_format()
    test_hex_dump()
