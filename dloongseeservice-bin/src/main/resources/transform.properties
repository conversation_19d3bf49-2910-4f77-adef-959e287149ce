spring.redis.host=default.cache.ip
spring.redis.port=default.cache.port
spring.redis.password=default.cache.password
spring.redis.database=default.cache.index
#primary database
spring.datasource.druid.primary.url=default.rdb.ip
spring.datasource.druid.primary.username=default.rdb.username
spring.datasource.druid.primary.password=default.rdb.password
#second database(烽理数据库)
spring.datasource.druid.second.url=second.rdb.ip
spring.datasource.druid.second.username=second.rdb.username
spring.datasource.druid.second.password=second.rdb.password
#rabbitmq
spring.rabbitmq.host=default.mq.ip
spring.rabbitmq.port=default.mq.port
spring.rabbitmq.username=default.mq.username
spring.rabbitmq.password=default.mq.password
#influxdb
spring.influx.url=influx.url
spring.influx.database=influx.database
spring.influx.user=influx.user
spring.influx.password=influx.password
