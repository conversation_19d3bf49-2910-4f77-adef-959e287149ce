# mysql
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=100MB
spring.servlet.multipart.max-request-size=100MB
spring.redis.jedis.pool.max-active=8
spring.redis.jedis.pool.min-idle=1
spring.redis.jedis.pool.max-idle=8
spring.redis.jedis.pool.max-wait=3000
spring.redis.timeout=5000
#多数据源配置
spring.datasource.druid.primary.mysql.driver-class-name=com.mysql.jdbc.cj.Driver
spring.datasource.druid.primary.initial-size=1
spring.datasource.druid.primary.max-active=24
spring.datasource.druid.primary.min-idle=2
spring.datasource.druid.primary.max-wait=1000
spring.datasource.druid.primary.validation-query=SELECT 1
spring.datasource.druid.primary.validation-query-timeout=1000
# 数据源2(烽理数据库)
spring.datasource.druid.second.mysql.driver-class-name=com.mysql.jdbc.cj.Driver
spring.datasource.druid.second.initial-size=1
spring.datasource.druid.second.max-active=10
spring.datasource.druid.second.min-idle=2
spring.datasource.druid.second.max-wait=10
spring.datasource.druid.second.validation-query=SELECT 1
spring.datasource.druid.second.validation-query-timeout=1000
spring.rabbitmq.listener.simple.retry.enabled=true
spring.rabbitmq.listener.simple.retry.max-attempts=3
spring.rabbitmq.listener.simple.retry.initial-interval=1000
#flyway配置
spring.flyway.enabled=false
spring.flyway.baseline-on-migrate=true
spring.flyway.baseline-version=0
# 线程池配置
threadpool.core-pool-size=2
threadpool.max-pool-size=50
threadpool.queue-capacity=1000
threadpool.keep-alive-seconds=300
# 交通事件线程配置
threadpool.trafficEvent.core-pool-size=5
threadpool.trafficEvent.max-pool-size=10
# 监控截屏线程配置
threadpool.snapshotHelper.core-pool-size=10
threadpool.snapshotHelper.max-pool-size=100
#拦截器规则
interceptor.paths=/wutos1/**
interceptor.ignorePaths=
interceptor.allowedMethods=GET,POST,PUT,OPTIONS,DELETE
mybatis-plus.global-config.db-config.sql-injector=com.baomidou.mybatisplus.mapper.LogicSqlInjector
mybatis-plus.global-config.db-config.logic-delete-value=0
mybatis-plus.global-config.db-config.logic-not-delete-value=1
# 开发环境
server.port=58654
spring.application.name=dloongsee
## 覆盖public配置
default.rdb.database=dloongsee_wuhu
second.rdb.database=dloongsee_fengli
default.cache.index=9
## nacos配置
# dataId，必填
nacos.dataId=template
# group，必填
nacos.group=fastrescue
# nacos地址，非必填，不填默认为***********:8848， 一般会在启动命令中带 --nacos-addr覆盖此配置
nacos.address=service.keepalived.fire.wutos:8848
# 是否读取nacos配置，非必填，启动命令中带 --enable-nacos 可覆盖此配置
nacos.enable=false
# 用户名
#nacos.username=nacos
# 密码
#nacos.password=nacos
# 使用ssl连接mysql
#nacos.use-ssl=true
swagger.enable=true
#配置中文需转换为unicode编码  https://www.w3cschool.cn/tools/index?name=unicode_chinese
car.province=鲁
car.city=ABGJW
#动应变原始数据存储路径
file.dir.dynamic=D:/bridgeStrain/
#上下行对应道路方向
upDir=left
#小里程->大里程 对应道路方向 l2r(left to right) r2l(right to left)
milDir=l2r
zabbix.url=http://***********:8008/zabbix/api_jsonrpc.php
zabbix.username=Admin
zabbix.password=zabbix
#是否开启使用本地测试监控获取截图
ffmpeg.test=false
# rabbitmq交换机
mq.exchange.car=parse.track.exchange.wuhu
mq.exchange.event=parse.event.exchange.wuhu
mq.exchange.lanefault=parse.lanefault.exchange.wuhu
# rabbitmq队列
mq.queue.grid.car=queue.track.grid.wuhu
mq.queue.grid.event=queue.event.grid.wuhu
mq.queue.prove.event=queue.event.prove.wuhu
#事件取证告警通知邮箱
prove.mail.to=<EMAIL>
#zabbix告警通知邮箱
zabbix.mail.to=<EMAIL>
#监控设备异常告警通知邮箱
monitor.mail.to=<EMAIL>
#道路监测类型，1：隧道+路面，2：上下行
trafficRoad.type=2
#轨迹最后一帧不处理
lastFrameModify=false
#起始里程，用于道路分段的桩号与板块的对应计算
road_segment_start_mil=0
#实时轨迹、事件的里程偏移量, 受milDir影响
rt_mil_offset=0
#轨迹第一帧是否忽略
firstFrameIgnore=false
#每日统计事件类型(为空统计全部)
statisticsEventType=OverSpeed,EmergencyLane,Stop
#时间占有率/平均时距检测断面的里程
section_mil=1000
#时间占有率/平均时距统计时间(秒)
section_duration=60
#交通流测点间距(米)
point_interval=50

#通行能力配置
#路段间距
traffic-capacity.interval=100
#统计时间(秒)
traffic-capacity.duration=300
#自由流速度(km/h)
traffic-capacity.free-speed=120
#拥堵速度阈值(km/h)
traffic-capacity.congestion-level.1.speed=30
traffic-capacity.congestion-level.2.speed=50
traffic-capacity.congestion-level.3.speed=70