########################################### primary
default.rdb.type=mysql
default.rdb.driver-class-name=com.mysql.cj.jdbc.Driver
default.rdb.database=test
default.rdb.ip=datasource.keepalived.fire.wutos
default.rdb.port=3306
default.rdb.username=root
default.rdb.password=dNVKbNCyC78n
########################################### second(烽理数据库)
second.rdb.type=mysql
second.rdb.driver-class-name=com.mysql.cj.jdbc.Driver
second.rdb.database=test
second.rdb.ip=datasource.keepalived.fire.wutos
second.rdb.port=3306
second.rdb.username=root
second.rdb.password=dNVKbNCyC78n

#
default.fdb.type=SEAWEEDFS_FILER
default.fdb.ip=datasource.keepalived.fire.wutos
default.fdb.port=8889

#
default.cache.type=redis
default.cache.ip=datasource.keepalived.fire.wutos
default.cache.port=6379
default.cache.password=wutos
default.cache.index=6

default.mq.type=rabbitmq
default.mq.ip=***********
default.mq.port=5672
default.mq.username=admin
default.mq.password=123456

#influxdb
influx.url=http://************:8086
influx.database=smartRoad_jga
influx.user=admin
influx.password=admin

# SSO（认证中心）服务器IP和端口，不能使用http://*:5002,必须固定IP和端口
sso.ip=service.keepalived.fire.wutos
sso.port=5002

#ZMQ
zmq.real.traffic.stream.url=tcp://localhost:9999
zmq.real.traffic.stream.topic=0x07,0x00,0x00,0x00

zmq.track.algorithms[0].name=906
zmq.track.algorithms[0].servers[0].url=tcp://***********:9031
zmq.track.algorithms[0].servers[0].direction=down
zmq.track.algorithms[0].servers[1].url=tcp://***********:9030
zmq.track.algorithms[0].servers[1].direction=up
zmq.track.algorithms[1].name=801
zmq.track.algorithms[1].servers[0].url=tcp://***********:9032
zmq.track.algorithms[1].servers[0].direction=up


zmq.event.algorithms[0].name=algorithms1
zmq.event.algorithms[0].desc=常规交通事件
zmq.event.algorithms[0].types=BACK_DRIVE,EMERGENCY_LANE,OVER_SPEED,LOW_SPEED,STOP,CHANGE_LANE,BLOCK,ACCIDENT
zmq.event.algorithms[0].enabled=true
zmq.event.algorithms[0].servers[0].url=tcp://***********:9031
zmq.event.algorithms[0].servers[0].topic=0x02,0x00,0x00,0x00
zmq.event.algorithms[0].servers[0].direction=down
zmq.event.algorithms[0].servers[1].url=tcp://***********:9033
zmq.event.algorithms[0].servers[1].topic=0x02,0x00,0x00,0x00
zmq.event.algorithms[0].servers[1].direction=up
zmq.event.algorithms[1].name=algorithms2
zmq.event.algorithms[1].desc=行人入侵事件
zmq.event.algorithms[1].types=PEDESTRIAN
zmq.event.algorithms[1].enabled=true
zmq.event.algorithms[1].servers[0].url=tcp://**********:8901
zmq.event.algorithms[1].servers[0].topic=0x09,0x00,0x00,0x00
zmq.event.algorithms[1].servers[0].direction=up
zmq.event.algorithms[1].servers[1].url=tcp://localhost:8885
zmq.event.algorithms[1].servers[1].topic=0x09,0x00,0x00,0x00
zmq.event.algorithms[1].servers[1].direction=down
zmq.event.algorithms[2].name=algorithms2
zmq.event.algorithms[2].desc=抛洒物事件
zmq.event.algorithms[2].types=SPRINKLE
zmq.event.algorithms[2].enabled=true
zmq.event.algorithms[2].servers[0].url=tcp://************:8001
zmq.event.algorithms[2].servers[0].topic=0x0A,0x00,0x00,0x00
zmq.event.algorithms[2].servers[0].direction=up
zmq.event.algorithms[2].servers[1].url=tcp://localhost:9885
zmq.event.algorithms[2].servers[1].topic=0x0A,0x00,0x00,0x00
zmq.event.algorithms[2].servers[1].direction=down
zmq.event.algorithms[3].name=algorithms3
zmq.event.algorithms[3].desc=行人入侵事件
zmq.event.algorithms[3].types=OBSTACLE
zmq.event.algorithms[3].enabled=true
zmq.event.algorithms[3].servers[0].url=tcp://************:8088
zmq.event.algorithms[3].servers[0].topic=0x02,0x00,0x00,0x00
zmq.event.algorithms[3].servers[0].direction=up

# zmq 车道数据异常
zmq.lanefault.servers[0].url=tcp://***********:8044
zmq.lanefault.servers[0].direction=down
zmq.lanefault.servers[0].topic=0x66,0x00,0x00,0x00
zmq.lanefault.servers[1].url=tcp://***********:8045
zmq.lanefault.servers[1].direction=up
zmq.lanefault.servers[1].topic=0x66,0x00,0x00,0x00
# 振动能量
# 车道顺序 asc:正序(1,2...9), desc:倒序(9...2,1)
zmq.energy.laneOrder=asc
zmq.energy.servers[0].url=tcp://***************:8044
zmq.energy.servers[0].direction=left
zmq.energy.servers[0].topic=0x61,0x00,0x00,0x00
zmq.energy.servers[1].url=tcp://***************:8054
zmq.energy.servers[1].direction=right
zmq.energy.servers[1].topic=0x61,0x00,0x00,0x00