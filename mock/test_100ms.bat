@echo off
chcp 65001 >nul

echo === 车辆轨迹数据100ms发送频率测试 ===
echo.

REM 检查Python版本
python --version
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.7+
    pause
    exit /b 1
)

REM 检查是否安装了pyzmq
python -c "import zmq" 2>nul
if errorlevel 1 (
    echo pyzmq未安装，正在安装...
    pip install pyzmq
)

echo.
echo 测试步骤：
echo 1. 启动车辆轨迹模拟器（100ms发送频率）
echo 2. 启动频率测试工具
echo.

REM 检查端口占用情况
echo 检查端口占用情况...
netstat -an | findstr ":9030" >nul
if not errorlevel 1 (
    echo 警告: 端口9030已被占用
)

netstat -an | findstr ":9031" >nul
if not errorlevel 1 (
    echo 警告: 端口9031已被占用
)

echo.
echo 选择操作：
echo 1) 启动模拟器
echo 2) 测试发送频率
echo 3) 同时启动模拟器和测试
echo.

set /p choice=请选择 (1-3): 

if "%choice%"=="1" (
    echo 启动车辆轨迹模拟器...
    python vehicle_track_simulator.py
) else if "%choice%"=="2" (
    echo 启动频率测试工具...
    python test_100ms_frequency.py
) else if "%choice%"=="3" (
    echo 同时启动模拟器和测试...
    REM 在后台启动模拟器
    start /B python vehicle_track_simulator.py
    
    REM 等待模拟器启动
    timeout /t 3 /nobreak >nul
    
    REM 启动测试工具
    python test_100ms_frequency.py
    
    echo 测试完成
) else (
    echo 无效选择
    pause
    exit /b 1
)

pause
