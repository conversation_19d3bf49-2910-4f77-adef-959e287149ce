#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试100ms发送频率的脚本
验证模拟器是否按照100ms间隔发送数据
"""

import zmq
import time
import struct
import threading
from datetime import datetime

class FrequencyTester:
    def __init__(self):
        self.context = zmq.Context()
        self.subscriber_up = None
        self.subscriber_down = None
        
        # 统计数据
        self.up_packets = []
        self.down_packets = []
        self.start_time = None
        self.running = True
        
    def setup_subscribers(self):
        """设置ZMQ订阅者"""
        try:
            # 上行数据订阅者
            self.subscriber_up = self.context.socket(zmq.SUB)
            self.subscriber_up.connect("tcp://localhost:9031")
            self.subscriber_up.setsockopt(zmq.SUBSCRIBE, b"")  # 订阅所有消息
            
            # 下行数据订阅者
            self.subscriber_down = self.context.socket(zmq.SUB)
            self.subscriber_down.connect("tcp://localhost:9030")
            self.subscriber_down.setsockopt(zmq.SUBSCRIBE, b"")  # 订阅所有消息
            
            print("ZMQ订阅者已连接")
            
        except Exception as e:
            print(f"ZMQ设置失败: {e}")
            raise
    
    def parse_packet_header(self, data):
        """解析数据包头"""
        if len(data) < 20:
            return None
            
        try:
            packet_sn = struct.unpack('<I', data[0:4])[0]
            device_ip = '.'.join(str(b) for b in data[4:8])
            timestamp = struct.unpack('<Q', data[8:16])[0]
            car_count = struct.unpack('<I', data[16:20])[0]
            
            return {
                'packet_sn': packet_sn,
                'device_ip': device_ip,
                'timestamp': timestamp,
                'car_count': car_count,
                'receive_time': time.time()
            }
        except Exception as e:
            print(f"解析数据包失败: {e}")
            return None
    
    def listen_up_direction(self):
        """监听上行数据"""
        print("开始监听上行数据...")
        while self.running:
            try:
                data = self.subscriber_up.recv(zmq.NOBLOCK)
                packet_info = self.parse_packet_header(data)
                if packet_info:
                    self.up_packets.append(packet_info)
                    print(f"上行数据 - 包序号: {packet_info['packet_sn']}, 车辆数: {packet_info['car_count']}")
            except zmq.Again:
                time.sleep(0.001)  # 1ms等待
            except Exception as e:
                print(f"上行数据接收错误: {e}")
                break
    
    def listen_down_direction(self):
        """监听下行数据"""
        print("开始监听下行数据...")
        while self.running:
            try:
                data = self.subscriber_down.recv(zmq.NOBLOCK)
                packet_info = self.parse_packet_header(data)
                if packet_info:
                    self.down_packets.append(packet_info)
                    print(f"下行数据 - 包序号: {packet_info['packet_sn']}, 车辆数: {packet_info['car_count']}")
            except zmq.Again:
                time.sleep(0.001)  # 1ms等待
            except Exception as e:
                print(f"下行数据接收错误: {e}")
                break
    
    def analyze_frequency(self, packets, direction_name):
        """分析发送频率"""
        if len(packets) < 2:
            print(f"{direction_name}: 数据包数量不足，无法分析频率")
            return
        
        print(f"\n=== {direction_name}频率分析 ===")
        print(f"总数据包数量: {len(packets)}")
        
        # 计算时间间隔
        intervals = []
        for i in range(1, len(packets)):
            interval = packets[i]['receive_time'] - packets[i-1]['receive_time']
            intervals.append(interval * 1000)  # 转换为毫秒
        
        if intervals:
            avg_interval = sum(intervals) / len(intervals)
            min_interval = min(intervals)
            max_interval = max(intervals)
            
            print(f"平均间隔: {avg_interval:.2f}ms")
            print(f"最小间隔: {min_interval:.2f}ms")
            print(f"最大间隔: {max_interval:.2f}ms")
            print(f"目标间隔: 100ms")
            print(f"频率偏差: {abs(avg_interval - 100):.2f}ms")
            
            # 检查是否在合理范围内（90-110ms）
            if 90 <= avg_interval <= 110:
                print("✓ 频率正常")
            else:
                print("✗ 频率异常")
        
        # 显示最近10个数据包的详细信息
        print(f"\n最近10个{direction_name}数据包:")
        recent_packets = packets[-10:] if len(packets) >= 10 else packets
        for i, packet in enumerate(recent_packets):
            timestamp_str = datetime.fromtimestamp(packet['timestamp']/1000).strftime('%H:%M:%S.%f')[:-3]
            print(f"  {i+1:2d}. 包序号: {packet['packet_sn']:4d}, 车辆数: {packet['car_count']:3d}, "
                  f"时间: {timestamp_str}")
    
    def run_test(self, duration=30):
        """运行测试"""
        print(f"=== 100ms发送频率测试 ===")
        print(f"测试时长: {duration}秒")
        print("请确保车辆轨迹模拟器正在运行...")
        print("=" * 50)
        
        self.setup_subscribers()
        self.start_time = time.time()
        
        # 启动监听线程
        up_thread = threading.Thread(target=self.listen_up_direction, daemon=True)
        down_thread = threading.Thread(target=self.listen_down_direction, daemon=True)
        
        up_thread.start()
        down_thread.start()
        
        try:
            # 等待指定时间
            time.sleep(duration)
            
        except KeyboardInterrupt:
            print("\n测试被用户中断")
        
        finally:
            self.running = False
            
            # 等待线程结束
            time.sleep(0.5)
            
            # 分析结果
            print("\n" + "=" * 50)
            print("测试结果分析:")
            self.analyze_frequency(self.up_packets, "上行")
            self.analyze_frequency(self.down_packets, "下行")
            
            # 总体统计
            total_packets = len(self.up_packets) + len(self.down_packets)
            test_duration = time.time() - self.start_time
            expected_packets = int(test_duration / 0.1) * 2  # 两个方向
            
            print(f"\n=== 总体统计 ===")
            print(f"测试时长: {test_duration:.2f}秒")
            print(f"接收数据包总数: {total_packets}")
            print(f"期望数据包总数: {expected_packets}")
            print(f"数据包接收率: {(total_packets/expected_packets*100):.1f}%")
            
            self.cleanup()
    
    def cleanup(self):
        """清理资源"""
        if self.subscriber_up:
            self.subscriber_up.close()
        if self.subscriber_down:
            self.subscriber_down.close()
        if self.context:
            self.context.term()
        print("测试完成，资源已清理")

def main():
    """主函数"""
    print("车辆轨迹数据100ms发送频率测试工具")
    print("请先启动 vehicle_track_simulator.py")
    
    input("按回车键开始测试...")
    
    tester = FrequencyTester()
    tester.run_test(30)  # 测试30秒

if __name__ == "__main__":
    main()
