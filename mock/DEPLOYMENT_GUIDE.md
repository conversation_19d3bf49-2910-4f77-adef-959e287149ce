# 车辆轨迹数据模拟器部署指南

## 快速开始

### 1. 环境准备

#### 系统要求
- Python 3.7 或更高版本
- 操作系统：Windows、Linux、macOS

#### 安装Python依赖
```bash
# 安装必需的依赖
pip install pyzmq

# 或者使用requirements.txt安装所有依赖
pip install -r requirements.txt
```

### 2. 快速测试

#### 运行简单测试
```bash
python simple_test.py
```
这个测试会验证数据格式是否正确，不需要ZMQ连接。

#### 运行数据格式测试
```bash
python test_data_format.py
```
这个测试会详细验证二进制数据格式。

### 3. 启动模拟器

#### Linux/macOS
```bash
# 使用启动脚本
./start_simulator.sh

# 或直接运行
python vehicle_track_simulator.py
```

#### Windows
```cmd
# 使用批处理文件
start_simulator.bat

# 或直接运行
python vehicle_track_simulator.py
```

## 详细配置

### 1. 修改配置参数

编辑 `vehicle_track_simulator.py` 文件中的配置参数：

```python
# 道路参数
self.MIN_MILEAGE = 1016020  # 最小里程
self.MAX_MILEAGE = 1176560  # 最大里程

# 车辆参数
self.TOTAL_VEHICLES = 500   # 车辆总数
self.SMALL_CAR_RATIO = 0.75 # 小型车比例

# 车速参数（米/秒）
self.SMALL_CAR_SPEED_RANGE = (22, 33)  # 小型车速度范围
self.LARGE_CAR_SPEED_RANGE = (17, 28)  # 大型车速度范围

# ZMQ配置
self.ZMQ_UP_PORT = 9031     # 上行端口
self.ZMQ_DOWN_PORT = 9030   # 下行端口
self.ZMQ_HOST = "localhost" # 主机地址
```

### 2. 自定义车辆行为

#### 修改速度变化概率
```python
# 在 update_vehicle_positions 方法中
if random.random() < 0.1:  # 10%概率调整速度
    # 速度调整逻辑
```

#### 修改变道概率
```python
# 在 update_vehicle_positions 方法中
if random.random() < 0.05:  # 5%概率变道
    vehicle['lane'] = random.choice(self.NORMAL_LANES)
```

### 3. 日志配置

#### 修改日志级别
```python
# 在文件开头修改
logging.basicConfig(level=logging.DEBUG)  # 详细日志
logging.basicConfig(level=logging.INFO)   # 标准日志
logging.basicConfig(level=logging.WARNING) # 仅警告和错误
```

## 与现有系统集成

### 1. 修改现有系统配置

在 `public.properties` 文件中确认ZMQ配置：

```properties
# 确保端口配置正确
zmq.track.algorithms[0].servers[0].url=tcp://localhost:9030
zmq.track.algorithms[0].servers[0].direction=down
zmq.track.algorithms[0].servers[1].url=tcp://localhost:9031
zmq.track.algorithms[0].servers[1].direction=up
```

### 2. 启动顺序

1. **先启动模拟器**：
   ```bash
   python vehicle_track_simulator.py
   ```

2. **再启动Java系统**：
   ```bash
   # 在dloongsee项目目录下
   java -jar dloongseeservice-bin/target/dloongsee-*.jar
   ```

### 3. 验证连接

#### 检查端口监听
```bash
# Linux/macOS
netstat -an | grep 903

# Windows
netstat -an | findstr 903
```

#### 查看Java系统日志
检查Java系统日志中是否有类似以下的输出：
```
ZMQ接收到track数据: CarTrackInbound(sn=1, deviceIp=*************, timestamp=..., count=250, carInfoDtoList=...)
```

## 性能调优

### 1. 调整车辆数量

根据系统性能调整车辆数量：
```python
# 低配置系统
self.TOTAL_VEHICLES = 100

# 高配置系统
self.TOTAL_VEHICLES = 1000
```

### 2. 调整发送频率

```python
# 更频繁的更新（0.5秒）
self.SEND_INTERVAL = 0.5

# 较慢的更新（2秒）
self.SEND_INTERVAL = 2.0
```

### 3. ZMQ缓冲区优化

```python
# 在setup_zmq方法中添加
self.publisher_up.setsockopt(zmq.SNDHWM, 1000)  # 发送高水位标记
self.publisher_down.setsockopt(zmq.SNDHWM, 1000)
```

## 故障排除

### 1. 常见错误

#### 端口被占用
```
Error: Address already in use
```
**解决方案**：
- 检查端口占用：`netstat -an | grep 903`
- 修改端口配置或停止占用端口的进程

#### ZMQ连接失败
```
zmq.error.ZMQError: No such device
```
**解决方案**：
- 检查pyzmq是否正确安装：`pip install pyzmq`
- 检查防火墙设置

#### 数据发送失败
```
zmq.Again: Resource temporarily unavailable
```
**解决方案**：
- 检查接收端是否正常运行
- 调整ZMQ缓冲区大小
- 降低发送频率

### 2. 调试技巧

#### 启用详细日志
```python
logging.basicConfig(level=logging.DEBUG, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
```

#### 监控系统资源
```bash
# 监控CPU和内存使用
top -p $(pgrep -f vehicle_track_simulator)

# 监控网络连接
ss -tulpn | grep 903
```

#### 数据包抓取
```bash
# 使用tcpdump抓取ZMQ数据包
sudo tcpdump -i lo -p tcp port 9030 or tcp port 9031
```

## 生产环境部署

### 1. 服务化部署

#### 创建systemd服务（Linux）
```ini
# /etc/systemd/system/vehicle-simulator.service
[Unit]
Description=Vehicle Track Simulator
After=network.target

[Service]
Type=simple
User=your-user
WorkingDirectory=/path/to/simulator
ExecStart=/usr/bin/python3 vehicle_track_simulator.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

#### 启动服务
```bash
sudo systemctl enable vehicle-simulator
sudo systemctl start vehicle-simulator
sudo systemctl status vehicle-simulator
```

### 2. Docker部署

#### 创建Dockerfile

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY .. .
EXPOSE 9030 9031

CMD ["python", "vehicle_track_simulator.py"]
```

#### 构建和运行
```bash
docker build -t vehicle-simulator .
docker run -p 9030:9030 -p 9031:9031 vehicle-simulator
```

### 3. 监控和告警

#### 健康检查脚本
```bash
#!/bin/bash
# health_check.sh

if netstat -an | grep -q ":9030.*LISTEN" && netstat -an | grep -q ":9031.*LISTEN"; then
    echo "Simulator is running"
    exit 0
else
    echo "Simulator is not running"
    exit 1
fi
```

#### 日志轮转
```bash
# 配置logrotate
/var/log/vehicle-simulator/*.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 644 your-user your-group
}
```
