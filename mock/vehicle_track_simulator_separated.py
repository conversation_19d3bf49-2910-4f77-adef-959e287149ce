#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
车辆轨迹数据模拟器 - 分离进程版本
上下行数据通过独立进程发送，避免相互影响
"""

import zmq
import time
import struct
import random
import multiprocessing
from datetime import datetime
from typing import List, Dict, Tuple
import json
import logging
import sys
import os

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DirectionalVehicleSimulator:
    def __init__(self, direction, port, vehicle_count=250):
        """
        单方向车辆模拟器
        :param direction: 0=下行(小里程→大里程), 1=上行(大里程→小里程)
        :param port: ZMQ端口
        :param vehicle_count: 车辆数量
        """
        self.direction = direction
        self.direction_name = "上行" if direction == 1 else "下行"
        self.port = port
        self.vehicle_count = vehicle_count
        
        # 道路参数
        self.MIN_MILEAGE = 1016020  # 最小里程（米）
        self.MAX_MILEAGE = 1176560  # 最大里程（米）
        
        # 车道配置
        self.NORMAL_LANES = [1, 2, 3]  # 正常行驶车道
        
        # 车辆参数
        self.SMALL_CAR_RATIO = 0.75  # 小型车比例75%
        
        # 车速参数（米/秒）
        self.SMALL_CAR_SPEED_RANGE = (22, 33)  # 小型车：80-120 km/h
        self.LARGE_CAR_SPEED_RANGE = (17, 28)  # 大型车：60-100 km/h
        
        # 数据发送频率
        self.SEND_INTERVAL = 0.1  # 100ms发送一次
        
        # 车辆状态
        self.vehicles: Dict[int, Dict] = {}
        self.packet_sequence = 0
        
        # ZMQ发布者
        self.context = zmq.Context()
        self.publisher = None
        
        self.initialize_vehicles()
        self.setup_zmq()
    
    def initialize_vehicles(self):
        """初始化车辆状态"""
        logger.info(f"[{self.direction_name}] 初始化 {self.vehicle_count} 辆车辆...")
        
        for vehicle_id in range(1, self.vehicle_count + 1):
            # 为不同方向的车辆添加ID偏移，避免冲突
            actual_id = vehicle_id + (self.direction * 1000)
            
            # 随机分配车辆类型
            is_small_car = random.random() < self.SMALL_CAR_RATIO
            car_type = 0 if is_small_car else 1
            
            # 根据车辆类型设置速度范围
            if is_small_car:
                speed = random.uniform(*self.SMALL_CAR_SPEED_RANGE)
            else:
                speed = random.uniform(*self.LARGE_CAR_SPEED_RANGE)
            
            # 根据方向设置初始里程
            if self.direction == 0:  # 下行：小里程→大里程
                initial_mileage = random.randint(self.MIN_MILEAGE, self.MIN_MILEAGE + 50000)
            else:  # 上行：大里程→小里程
                initial_mileage = random.randint(self.MAX_MILEAGE - 50000, self.MAX_MILEAGE)
            
            # 选择车道
            lane = random.choice(self.NORMAL_LANES)
            
            # 生成车牌号
            car_number = self.generate_car_number()
            
            self.vehicles[actual_id] = {
                'car_id': actual_id,
                'car_number': car_number,
                'car_type': car_type,
                'speed': speed,
                'lane': lane,
                'mileage': initial_mileage,
                'direction': self.direction,
                'last_update': time.time()
            }
        
        small_car_count = sum(1 for v in self.vehicles.values() if v['car_type'] == 0)
        large_car_count = sum(1 for v in self.vehicles.values() if v['car_type'] == 1)
        logger.info(f"[{self.direction_name}] 车辆初始化完成。小型车：{small_car_count} 辆，大型车：{large_car_count} 辆")
    
    def generate_car_number(self) -> str:
        """生成随机车牌号"""
        provinces = ['鲁', '京', '沪', '津', '渝', '冀', '晋', '蒙', '辽', '吉', '黑', '苏', '浙', '皖', '闽', '赣', '豫', '鄂', '湘', '粤', '桂', '琼', '川', '贵', '云', '藏', '陕', '甘', '青', '宁', '新']
        letters = 'ABCDEFGHJKLMNPQRSTUVWXYZ'
        digits = '0123456789'
        
        province = random.choice(provinces)
        city_code = random.choice(letters)
        suffix = ''.join(random.choices(digits + letters, k=5))
        
        return f"{province}{city_code}{suffix}"
    
    def setup_zmq(self):
        """设置ZMQ发布者"""
        try:
            self.publisher = self.context.socket(zmq.PUB)
            self.publisher.bind(f"tcp://*:{self.port}")
            
            logger.info(f"[{self.direction_name}] ZMQ发布者已启动 - 端口: {self.port}")
            time.sleep(1)  # 等待连接建立
            
        except Exception as e:
            logger.error(f"[{self.direction_name}] ZMQ设置失败: {e}")
            raise
    
    def update_vehicle_positions(self):
        """更新车辆位置"""
        current_time = time.time()
        vehicles_to_remove = []
        
        for vehicle_id, vehicle in self.vehicles.items():
            time_delta = current_time - vehicle['last_update']
            distance_moved = vehicle['speed'] * time_delta
            
            if self.direction == 0:  # 下行：小里程→大里程
                vehicle['mileage'] += distance_moved
                if vehicle['mileage'] > self.MAX_MILEAGE:
                    vehicles_to_remove.append(vehicle_id)
            else:  # 上行：大里程→小里程
                vehicle['mileage'] -= distance_moved
                if vehicle['mileage'] < self.MIN_MILEAGE:
                    vehicles_to_remove.append(vehicle_id)
            
            vehicle['last_update'] = current_time
            
            # 随机变化车速（模拟真实交通）
            if random.random() < 0.1:  # 10%概率调整速度
                if vehicle['car_type'] == 0:  # 小型车
                    speed_change = random.uniform(-2, 2)
                    vehicle['speed'] = max(self.SMALL_CAR_SPEED_RANGE[0], 
                                         min(self.SMALL_CAR_SPEED_RANGE[1], 
                                             vehicle['speed'] + speed_change))
                else:  # 大型车
                    speed_change = random.uniform(-1.5, 1.5)
                    vehicle['speed'] = max(self.LARGE_CAR_SPEED_RANGE[0], 
                                         min(self.LARGE_CAR_SPEED_RANGE[1], 
                                             vehicle['speed'] + speed_change))
            
            # 随机变道（5%概率）
            if random.random() < 0.05:
                vehicle['lane'] = random.choice(self.NORMAL_LANES)
        
        # 移除已驶出道路的车辆并生成新车辆
        for vehicle_id in vehicles_to_remove:
            self.respawn_vehicle(vehicle_id)
    
    def respawn_vehicle(self, vehicle_id: int):
        """重新生成车辆"""
        vehicle = self.vehicles[vehicle_id]
        
        # 根据方向设置初始里程
        if self.direction == 0:  # 下行
            initial_mileage = random.randint(self.MIN_MILEAGE, self.MIN_MILEAGE + 10000)
        else:  # 上行
            initial_mileage = random.randint(self.MAX_MILEAGE - 10000, self.MAX_MILEAGE)
        
        vehicle.update({
            'mileage': initial_mileage,
            'lane': random.choice(self.NORMAL_LANES),
            'last_update': time.time()
        })
    
    def pack_vehicle_data(self, vehicles: List[Dict]) -> bytes:
        """将车辆数据打包成二进制格式"""
        self.packet_sequence += 1
        
        # 包头数据
        packet_sn = self.packet_sequence
        device_ip = struct.pack('<4B', 192, 168, 1, 100 + self.direction)  # 不同方向使用不同IP
        timestamp = int(time.time() * 1000)  # 毫秒时间戳
        car_count = len(vehicles)
        
        # 打包包头（20字节）
        header = struct.pack('<I', packet_sn)  # 包序列号
        header += device_ip  # 设备IP
        header += struct.pack('<Q', timestamp)  # 时间戳
        header += struct.pack('<I', car_count)  # 车辆数量
        
        # 打包车辆数据
        car_data = b''
        for vehicle in vehicles:
            # 车辆ID（8字节，实际只用4字节，补0）
            car_id_bytes = struct.pack('<I', vehicle['car_id']) + b'\x00\x00\x00\x00'
            
            # 车牌号（16字节，UTF-8编码，不足补0）
            car_number = vehicle['car_number'].encode('utf-8')[:16]
            car_number_bytes = car_number.ljust(16, b'\x00')
            
            # 车辆类型（1字节）
            car_type_byte = struct.pack('<B', vehicle['car_type'])
            
            # 影响里程范围（8字节，两个4字节整数）
            scope_start = int(vehicle['mileage'] - 50)
            scope_end = int(vehicle['mileage'] + 50)
            scope_bytes = struct.pack('<II', scope_start, scope_end)
            
            # 车速（4字节浮点数）
            speed_bytes = struct.pack('<f', vehicle['speed'])
            
            # 车道号（1字节）
            lane_byte = struct.pack('<B', vehicle['lane'])
            
            # 里程（4字节整数）
            mileage_bytes = struct.pack('<I', int(vehicle['mileage']))
            
            # 预留字段（1字节）
            extend1_byte = struct.pack('<B', 0)
            
            # 方向（1字节）
            direction_byte = struct.pack('<B', vehicle['direction'])
            
            # 组装单个车辆数据（44字节）
            single_car_data = (car_id_bytes + car_number_bytes + car_type_byte + 
                             scope_bytes + speed_bytes + lane_byte + 
                             mileage_bytes + extend1_byte + direction_byte)
            
            car_data += single_car_data
        
        return header + car_data
    
    def send_data(self):
        """发送数据到ZMQ"""
        vehicles = list(self.vehicles.values())
        
        if vehicles:
            data = self.pack_vehicle_data(vehicles)
            try:
                self.publisher.send(data, zmq.NOBLOCK)
                logger.debug(f"[{self.direction_name}] 发送数据: {len(vehicles)} 辆车")
            except zmq.Again:
                logger.warning(f"[{self.direction_name}] 数据发送缓冲区满")
    
    def print_statistics(self):
        """打印统计信息"""
        vehicle_count = len(self.vehicles)
        small_car_count = sum(1 for v in self.vehicles.values() if v['car_type'] == 0)
        large_car_count = sum(1 for v in self.vehicles.values() if v['car_type'] == 1)
        avg_speed = sum(v['speed'] for v in self.vehicles.values()) / max(vehicle_count, 1)
        
        logger.info(f"[{self.direction_name}] 统计信息 [包序号: {self.packet_sequence}] - "
                   f"车辆: {vehicle_count}辆 (平均速度: {avg_speed:.1f}m/s), "
                   f"小型车: {small_car_count}辆, 大型车: {large_car_count}辆")
    
    def run(self):
        """主运行循环"""
        logger.info(f"[{self.direction_name}] 开始车辆轨迹模拟...")
        
        try:
            while True:
                start_time = time.time()
                
                # 更新车辆位置
                self.update_vehicle_positions()
                
                # 发送数据
                self.send_data()
                
                # 每5秒打印一次统计信息（50次循环 = 5秒）
                if self.packet_sequence % 50 == 0:
                    self.print_statistics()
                
                # 控制发送频率
                elapsed = time.time() - start_time
                sleep_time = max(0, self.SEND_INTERVAL - elapsed)
                time.sleep(sleep_time)
                
        except KeyboardInterrupt:
            logger.info(f"[{self.direction_name}] 接收到停止信号，正在关闭...")
        except Exception as e:
            logger.error(f"[{self.direction_name}] 运行时错误: {e}")
        finally:
            self.cleanup()
    
    def cleanup(self):
        """清理资源"""
        if self.publisher:
            self.publisher.close()
        if self.context:
            self.context.term()
        logger.info(f"[{self.direction_name}] 资源清理完成")


def run_direction_simulator(direction, port):
    """运行单方向模拟器的进程函数"""
    simulator = DirectionalVehicleSimulator(direction, port)
    simulator.run()


def main():
    """主函数 - 启动两个独立进程"""
    print("=== 车辆轨迹数据模拟器 - 分离进程版本 ===")
    print(f"模拟参数:")
    print(f"- 车辆数量: 500辆 (每个方向250辆)")
    print(f"- 里程范围: 1016020m - 1176560m (约160.54公里)")
    print(f"- 可用车道: 1, 2, 3")
    print(f"- 小型车速度: 22-33 m/s (80-120 km/h)")
    print(f"- 大型车速度: 17-28 m/s (60-100 km/h)")
    print(f"- 上行端口: 9031, 下行端口: 9030")
    print(f"- 数据发送频率: 100ms/次 (每次都保证有数据)")
    print(f"- 进程模式: 上下行独立进程，避免相互影响")
    print("=" * 50)
    
    try:
        # 创建两个独立进程
        down_process = multiprocessing.Process(
            target=run_direction_simulator, 
            args=(0, 9030),  # 下行方向，端口9030
            name="DownDirection"
        )
        
        up_process = multiprocessing.Process(
            target=run_direction_simulator, 
            args=(1, 9031),  # 上行方向，端口9031
            name="UpDirection"
        )
        
        # 启动进程
        logger.info("启动下行方向模拟器进程...")
        down_process.start()
        
        logger.info("启动上行方向模拟器进程...")
        up_process.start()
        
        logger.info("两个方向的模拟器进程已启动，按Ctrl+C停止")
        
        # 等待进程结束
        down_process.join()
        up_process.join()
        
    except KeyboardInterrupt:
        logger.info("接收到停止信号，正在关闭所有进程...")
        
        # 终止进程
        if down_process.is_alive():
            down_process.terminate()
            down_process.join(timeout=5)
        
        if up_process.is_alive():
            up_process.terminate()
            up_process.join(timeout=5)
        
        logger.info("所有进程已关闭")
    
    except Exception as e:
        logger.error(f"主进程错误: {e}")


if __name__ == "__main__":
    # 设置多进程启动方法
    multiprocessing.set_start_method('spawn', force=True)
    main()
