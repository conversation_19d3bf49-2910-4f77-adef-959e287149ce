#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
车辆轨迹数据模拟器
模拟500辆车在高速公路上的运行轨迹数据，通过ZMQ发送给现有系统
"""

import zmq
import time
import struct
import random
import threading
import multiprocessing
from datetime import datetime
from typing import List, Dict, Tuple
import json
import logging
import sys

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class VehicleTrackSimulator:
    def __init__(self):
        # 道路参数
        self.MIN_MILEAGE = 0  # 最小里程（米）
        self.MAX_MILEAGE = 160540  # 最大里程（米）
        self.TOTAL_DISTANCE = self.MAX_MILEAGE - self.MIN_MILEAGE  # 总长度约160.54公里
        
        # 车道配置
        self.AVAILABLE_LANES = [1, 2, 3, 9]  # 可用车道
        self.NORMAL_LANES = [1, 2, 3]  # 正常行驶车道
        self.EMERGENCY_LANE = 9  # 应急车道
        
        # 车辆参数
        self.TOTAL_VEHICLES = 500
        self.SMALL_CAR_RATIO = 0.75  # 小型车比例75%
        
        # 车速参数（米/秒）
        self.SMALL_CAR_SPEED_RANGE = (22, 33)  # 小型车：80-120 km/h
        self.LARGE_CAR_SPEED_RANGE = (17, 28)  # 大型车：60-100 km/h
        
        # ZMQ配置
        self.ZMQ_UP_PORT = 9031    # 上行方向端口（大里程→小里程）
        self.ZMQ_DOWN_PORT = 9030  # 下行方向端口（小里程→大里程）
        self.ZMQ_HOST = "localhost"
        
        # 数据发送频率
        self.SEND_INTERVAL = 0.1  # 100ms发送一次
        
        # 车辆状态
        self.vehicles: Dict[int, Dict] = {}
        self.packet_sequence = 0
        
        # ZMQ发布者
        self.context = zmq.Context()
        self.publisher_up = None
        self.publisher_down = None
        
        self.initialize_vehicles()
        self.setup_zmq()
    
    def initialize_vehicles(self):
        """初始化车辆状态"""
        logger.info(f"初始化 {self.TOTAL_VEHICLES} 辆车辆...")
        
        for vehicle_id in range(1, self.TOTAL_VEHICLES + 1):
            # 随机分配车辆类型
            is_small_car = random.random() < self.SMALL_CAR_RATIO
            car_type = 0 if is_small_car else 1
            
            # 根据车辆类型设置速度范围
            if is_small_car:
                speed = random.uniform(*self.SMALL_CAR_SPEED_RANGE)
            else:
                speed = random.uniform(*self.LARGE_CAR_SPEED_RANGE)
            
            # 随机选择方向（0：下行，1：上行）
            direction = random.randint(0, 1)
            
            # 根据方向设置初始里程
            if direction == 0:  # 下行：小里程→大里程
                initial_mileage = random.randint(self.MIN_MILEAGE, self.MIN_MILEAGE + 50000)
            else:  # 上行：大里程→小里程
                initial_mileage = random.randint(self.MAX_MILEAGE - 50000, self.MAX_MILEAGE)
            
            # 选择车道（避免在应急车道生成正常行驶车辆）
            lane = random.choice(self.NORMAL_LANES)
            
            # 生成车牌号
            car_number = self.generate_car_number()
            
            self.vehicles[vehicle_id] = {
                'car_id': vehicle_id,
                'car_number': car_number,
                'car_type': car_type,
                'speed': speed,
                'lane': lane,
                'mileage': initial_mileage,
                'direction': direction,
                'last_update': time.time()
            }
        
        logger.info(f"车辆初始化完成。小型车：{sum(1 for v in self.vehicles.values() if v['car_type'] == 0)} 辆，"
                   f"大型车：{sum(1 for v in self.vehicles.values() if v['car_type'] == 1)} 辆")
    
    def generate_car_number(self) -> str:
        """生成随机车牌号"""
        provinces = ['鲁', '京', '沪', '津', '渝', '冀', '晋', '蒙', '辽', '吉', '黑', '苏', '浙', '皖', '闽', '赣', '豫', '鄂', '湘', '粤', '桂', '琼', '川', '贵', '云', '藏', '陕', '甘', '青', '宁', '新']
        letters = 'ABCDEFGHJKLMNPQRSTUVWXYZ'
        digits = '0123456789'
        
        province = random.choice(provinces)
        city_code = random.choice(letters)
        
        # 生成5位数字或字母数字组合
        suffix = ''.join(random.choices(digits + letters, k=5))
        
        return f"{province}{city_code}{suffix}"
    
    def setup_zmq(self):
        """设置ZMQ发布者"""
        try:
            # 上行方向发布者
            self.publisher_up = self.context.socket(zmq.PUB)
            self.publisher_up.bind(f"tcp://*:{self.ZMQ_UP_PORT}")
            
            # 下行方向发布者
            self.publisher_down = self.context.socket(zmq.PUB)
            self.publisher_down.bind(f"tcp://*:{self.ZMQ_DOWN_PORT}")
            
            logger.info(f"ZMQ发布者已启动 - 上行端口: {self.ZMQ_UP_PORT}, 下行端口: {self.ZMQ_DOWN_PORT}")
            
            # 等待连接建立
            time.sleep(1)
            
        except Exception as e:
            logger.error(f"ZMQ设置失败: {e}")
            raise
    
    def update_vehicle_positions(self):
        """更新车辆位置"""
        current_time = time.time()
        vehicles_to_remove = []
        
        for vehicle_id, vehicle in self.vehicles.items():
            time_delta = current_time - vehicle['last_update']
            distance_moved = vehicle['speed'] * time_delta
            
            if vehicle['direction'] == 0:  # 下行：小里程→大里程
                vehicle['mileage'] += distance_moved
                if vehicle['mileage'] > self.MAX_MILEAGE:
                    vehicles_to_remove.append(vehicle_id)
            else:  # 上行：大里程→小里程
                vehicle['mileage'] -= distance_moved
                if vehicle['mileage'] < self.MIN_MILEAGE:
                    vehicles_to_remove.append(vehicle_id)
            
            vehicle['last_update'] = current_time
            
            # 随机变化车速（模拟真实交通）
            if random.random() < 0.1:  # 10%概率调整速度
                if vehicle['car_type'] == 0:  # 小型车
                    speed_change = random.uniform(-2, 2)
                    vehicle['speed'] = max(self.SMALL_CAR_SPEED_RANGE[0], 
                                         min(self.SMALL_CAR_SPEED_RANGE[1], 
                                             vehicle['speed'] + speed_change))
                else:  # 大型车
                    speed_change = random.uniform(-1.5, 1.5)
                    vehicle['speed'] = max(self.LARGE_CAR_SPEED_RANGE[0], 
                                         min(self.LARGE_CAR_SPEED_RANGE[1], 
                                             vehicle['speed'] + speed_change))
            
            # 随机变道（5%概率）
            if random.random() < 0.05:
                vehicle['lane'] = random.choice(self.NORMAL_LANES)
        
        # 移除已驶出道路的车辆并生成新车辆
        for vehicle_id in vehicles_to_remove:
            self.respawn_vehicle(vehicle_id)
    
    def respawn_vehicle(self, vehicle_id: int):
        """重新生成车辆"""
        # 保持车辆类型和基本属性，重新设置位置
        vehicle = self.vehicles[vehicle_id]
        
        # 随机选择新的方向
        direction = random.randint(0, 1)
        
        # 根据方向设置初始里程
        if direction == 0:  # 下行
            initial_mileage = random.randint(self.MIN_MILEAGE, self.MIN_MILEAGE + 10000)
        else:  # 上行
            initial_mileage = random.randint(self.MAX_MILEAGE - 10000, self.MAX_MILEAGE)
        
        vehicle.update({
            'mileage': initial_mileage,
            'direction': direction,
            'lane': random.choice(self.NORMAL_LANES),
            'last_update': time.time()
        })

    def force_generate_vehicles(self):
        """强制生成一些车辆，确保有数据发送"""
        logger.info("强制生成车辆以确保数据发送")

        # 如果车辆数量太少，生成一些新车辆
        if len(self.vehicles) < 10:
            for i in range(10):
                vehicle_id = max(self.vehicles.keys()) + 1 if self.vehicles else 1

                # 随机分配车辆类型
                is_small_car = random.random() < self.SMALL_CAR_RATIO
                car_type = 0 if is_small_car else 1

                # 根据车辆类型设置速度
                if is_small_car:
                    speed = random.uniform(*self.SMALL_CAR_SPEED_RANGE)
                else:
                    speed = random.uniform(*self.LARGE_CAR_SPEED_RANGE)

                # 随机选择方向
                direction = random.randint(0, 1)

                # 根据方向设置初始里程
                if direction == 0:  # 下行
                    initial_mileage = random.randint(self.MIN_MILEAGE, self.MIN_MILEAGE + 20000)
                else:  # 上行
                    initial_mileage = random.randint(self.MAX_MILEAGE - 20000, self.MAX_MILEAGE)

                # 生成车牌号
                car_number = self.generate_car_number()

                self.vehicles[vehicle_id] = {
                    'car_id': vehicle_id,
                    'car_number': car_number,
                    'car_type': car_type,
                    'speed': speed,
                    'lane': random.choice(self.NORMAL_LANES),
                    'mileage': initial_mileage,
                    'direction': direction,
                    'last_update': time.time()
                }

    def force_generate_vehicles(self):
        """强制生成一些车辆，确保有数据发送"""
        logger.info("强制生成车辆以确保数据发送")

        # 如果车辆数量太少，生成一些新车辆
        if len(self.vehicles) < 10:
            for i in range(10):
                vehicle_id = max(self.vehicles.keys()) + 1 if self.vehicles else 1

                # 随机分配车辆类型
                is_small_car = random.random() < self.SMALL_CAR_RATIO
                car_type = 0 if is_small_car else 1

                # 根据车辆类型设置速度
                if is_small_car:
                    speed = random.uniform(*self.SMALL_CAR_SPEED_RANGE)
                else:
                    speed = random.uniform(*self.LARGE_CAR_SPEED_RANGE)

                # 随机选择方向
                direction = random.randint(0, 1)

                # 根据方向设置初始里程
                if direction == 0:  # 下行
                    initial_mileage = random.randint(self.MIN_MILEAGE, self.MIN_MILEAGE + 20000)
                else:  # 上行
                    initial_mileage = random.randint(self.MAX_MILEAGE - 20000, self.MAX_MILEAGE)

                # 生成车牌号
                car_number = self.generate_car_number()

                self.vehicles[vehicle_id] = {
                    'car_id': vehicle_id,
                    'car_number': car_number,
                    'car_type': car_type,
                    'speed': speed,
                    'lane': random.choice(self.NORMAL_LANES),
                    'mileage': initial_mileage,
                    'direction': direction,
                    'last_update': time.time()
                }

    def pack_vehicle_data(self, vehicles: List[Dict]) -> bytes:
        """
        将车辆数据打包成二进制格式
        数据格式（小端序）：
        - 包序列号：4字节
        - 设备IP：4字节
        - 时间戳：8字节
        - 车辆数量：4字节
        - 车辆数据：每辆车44字节
        """
        self.packet_sequence += 1

        # 包头数据
        packet_sn = self.packet_sequence
        device_ip = struct.pack('<4B', 192, 168, 1, 100)  # 模拟设备IP
        timestamp = int(time.time() * 1000)  # 毫秒时间戳
        car_count = len(vehicles)

        # 打包包头（20字节）
        header = struct.pack('<I', packet_sn)  # 包序列号
        header += device_ip  # 设备IP
        header += struct.pack('<Q', timestamp)  # 时间戳
        header += struct.pack('<I', car_count)  # 车辆数量

        # 打包车辆数据
        car_data = b''
        for vehicle in vehicles:
            # 车辆ID（8字节，实际只用4字节，补0）
            car_id_bytes = struct.pack('<I', vehicle['car_id']) + b'\x00\x00\x00\x00'

            # 车牌号（16字节，UTF-8编码，不足补0）
            car_number = vehicle['car_number'].encode('utf-8')[:16]
            car_number_bytes = car_number.ljust(16, b'\x00')

            # 车辆类型（1字节）
            car_type_byte = struct.pack('<B', vehicle['car_type'])

            # 影响里程范围（8字节，两个4字节整数）
            scope_start = int(vehicle['mileage'] - 50)
            scope_end = int(vehicle['mileage'] + 50)
            scope_bytes = struct.pack('<II', scope_start, scope_end)

            # 车速（4字节浮点数）
            speed_bytes = struct.pack('<f', vehicle['speed'])

            # 车道号（1字节）
            lane_byte = struct.pack('<B', vehicle['lane'])

            # 里程（4字节整数）
            mileage_bytes = struct.pack('<I', int(vehicle['mileage']))

            # 预留字段（1字节）
            extend1_byte = struct.pack('<B', 0)

            # 方向（1字节）
            direction_byte = struct.pack('<B', vehicle['direction'])

            # 组装单个车辆数据（44字节）
            single_car_data = (car_id_bytes + car_number_bytes + car_type_byte +
                             scope_bytes + speed_bytes + lane_byte +
                             mileage_bytes + extend1_byte + direction_byte)

            car_data += single_car_data

        return header + car_data

    def send_data(self):
        """发送数据到ZMQ - 确保每次都有数据发送"""
        # 分离上行和下行车辆
        up_vehicles = [v for v in self.vehicles.values() if v['direction'] == 1]
        down_vehicles = [v for v in self.vehicles.values() if v['direction'] == 0]

        # 确保至少有一些车辆数据发送
        if not up_vehicles and not down_vehicles:
            logger.warning("没有车辆数据，强制生成一些车辆")
            self.force_generate_vehicles()
            up_vehicles = [v for v in self.vehicles.values() if v['direction'] == 1]
            down_vehicles = [v for v in self.vehicles.values() if v['direction'] == 0]

        # 发送上行数据 - 总是发送，即使没有车辆也发送空数据包
        up_data = self.pack_vehicle_data(up_vehicles)
        try:
            self.publisher_up.send(up_data, zmq.NOBLOCK)
            logger.debug(f"发送上行数据: {len(up_vehicles)} 辆车")
        except zmq.Again:
            logger.warning("上行数据发送缓冲区满")

        # 发送下行数据 - 总是发送，即使没有车辆也发送空数据包
        down_data = self.pack_vehicle_data(down_vehicles)
        try:
            self.publisher_down.send(down_data, zmq.NOBLOCK)
            logger.debug(f"发送下行数据: {len(down_vehicles)} 辆车")
        except zmq.Again:
            logger.warning("下行数据发送缓冲区满")

    def print_statistics(self):
        """打印统计信息"""
        up_count = sum(1 for v in self.vehicles.values() if v['direction'] == 1)
        down_count = sum(1 for v in self.vehicles.values() if v['direction'] == 0)

        small_car_count = sum(1 for v in self.vehicles.values() if v['car_type'] == 0)
        large_car_count = sum(1 for v in self.vehicles.values() if v['car_type'] == 1)

        avg_speed_up = sum(v['speed'] for v in self.vehicles.values() if v['direction'] == 1) / max(up_count, 1)
        avg_speed_down = sum(v['speed'] for v in self.vehicles.values() if v['direction'] == 0) / max(down_count, 1)

        logger.info(f"统计信息 [包序号: {self.packet_sequence}] - 上行: {up_count}辆 (平均速度: {avg_speed_up:.1f}m/s), "
                   f"下行: {down_count}辆 (平均速度: {avg_speed_down:.1f}m/s), "
                   f"小型车: {small_car_count}辆, 大型车: {large_car_count}辆, "
                   f"发送频率: 100ms/次")

    def run(self):
        """主运行循环"""
        logger.info("开始车辆轨迹模拟...")

        try:
            while True:
                start_time = time.time()

                # 更新车辆位置
                self.update_vehicle_positions()

                # 发送数据
                self.send_data()

                # 每5秒打印一次统计信息（50次循环 = 5秒）
                if self.packet_sequence % 50 == 0:
                    self.print_statistics()

                # 控制发送频率
                elapsed = time.time() - start_time
                sleep_time = max(0, self.SEND_INTERVAL - elapsed)
                time.sleep(sleep_time)

        except KeyboardInterrupt:
            logger.info("接收到停止信号，正在关闭...")
        except Exception as e:
            logger.error(f"运行时错误: {e}")
        finally:
            self.cleanup()

    def cleanup(self):
        """清理资源"""
        if self.publisher_up:
            self.publisher_up.close()
        if self.publisher_down:
            self.publisher_down.close()
        if self.context:
            self.context.term()
        logger.info("资源清理完成")


def main():
    """主函数"""
    print("=== 车辆轨迹数据模拟器 ===")
    print(f"模拟参数:")
    print(f"- 车辆数量: 500辆")
    print(f"- 里程范围: 1016020m - 1176560m (约160.54公里)")
    print(f"- 可用车道: 1, 2, 3, 9 (主要使用1, 2, 3)")
    print(f"- 小型车速度: 22-33 m/s (80-120 km/h)")
    print(f"- 大型车速度: 17-28 m/s (60-100 km/h)")
    print(f"- 上行端口: 9031, 下行端口: 9030")
    print(f"- 数据发送频率: 100ms/次 (每次都保证有数据)")
    print("=" * 40)

    simulator = VehicleTrackSimulator()
    simulator.run()


if __name__ == "__main__":
    main()
