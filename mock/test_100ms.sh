#!/bin/bash

# 100ms发送频率测试脚本

echo "=== 车辆轨迹数据100ms发送频率测试 ==="
echo ""

# 检查Python版本
python_version=$(python3 --version 2>&1 | awk '{print $2}')
echo "Python版本: $python_version"

# 检查是否安装了pyzmq
if python3 -c "import zmq" 2>/dev/null; then
    echo "✓ pyzmq已安装"
else
    echo "✗ pyzmq未安装，正在安装..."
    pip3 install pyzmq
fi

echo ""
echo "测试步骤："
echo "1. 启动车辆轨迹模拟器（100ms发送频率）"
echo "2. 启动频率测试工具"
echo ""

# 检查端口是否被占用
echo "检查端口占用情况..."
if netstat -an 2>/dev/null | grep -q ":9030"; then
    echo "⚠️  端口9030已被占用"
fi

if netstat -an 2>/dev/null | grep -q ":9031"; then
    echo "⚠️  端口9031已被占用"
fi

echo ""
echo "选择操作："
echo "1) 启动模拟器"
echo "2) 测试发送频率"
echo "3) 同时启动模拟器和测试"
echo ""

read -p "请选择 (1-3): " choice

case $choice in
    1)
        echo "启动车辆轨迹模拟器..."
        python3 vehicle_track_simulator.py
        ;;
    2)
        echo "启动频率测试工具..."
        python3 test_100ms_frequency.py
        ;;
    3)
        echo "同时启动模拟器和测试..."
        # 在后台启动模拟器
        python3 vehicle_track_simulator.py &
        SIMULATOR_PID=$!
        
        # 等待模拟器启动
        sleep 3
        
        # 启动测试工具
        python3 test_100ms_frequency.py
        
        # 测试完成后停止模拟器
        echo "停止模拟器..."
        kill $SIMULATOR_PID 2>/dev/null
        ;;
    *)
        echo "无效选择"
        exit 1
        ;;
esac
