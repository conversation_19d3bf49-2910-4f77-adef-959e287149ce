@echo off
chcp 65001 >nul

echo === 车辆轨迹数据模拟器启动脚本 ===

REM 检查Python版本
python --version
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.7+
    pause
    exit /b 1
)

REM 检查是否安装了pyzmq
python -c "import zmq" 2>nul
if errorlevel 1 (
    echo pyzmq未安装，正在安装...
    pip install pyzmq
)

REM 检查端口占用情况
echo 检查端口占用情况...
netstat -an | findstr ":9030" >nul
if not errorlevel 1 (
    echo 警告: 端口9030已被占用
)

netstat -an | findstr ":9031" >nul
if not errorlevel 1 (
    echo 警告: 端口9031已被占用
)

REM 创建日志目录
if not exist logs mkdir logs

REM 启动模拟器
echo 启动车辆轨迹数据模拟器...
python vehicle_track_simulator.py

pause
