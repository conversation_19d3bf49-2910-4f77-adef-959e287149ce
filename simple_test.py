#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本
用于快速验证模拟器的基本功能
"""

import struct
import time
import random

def create_test_data():
    """创建测试数据"""
    print("=== 创建测试数据 ===")
    
    # 模拟车辆数据
    vehicles = [
        {
            'car_id': 1001,
            'car_number': '鲁A12345',
            'car_type': 0,  # 小型车
            'speed': 25.5,  # m/s
            'lane': 1,
            'mileage': 1050000,
            'direction': 0  # 下行
        },
        {
            'car_id': 1002,
            'car_number': '京B67890',
            'car_type': 1,  # 大型车
            'speed': 20.0,  # m/s
            'lane': 2,
            'mileage': 1080000,
            'direction': 1  # 上行
        }
    ]
    
    return vehicles

def pack_data(vehicles):
    """打包数据"""
    print("=== 打包数据 ===")
    
    # 包头数据
    packet_sn = 1
    device_ip = struct.pack('<4B', 192, 168, 1, 100)
    timestamp = int(time.time() * 1000)
    car_count = len(vehicles)
    
    # 打包包头
    header = struct.pack('<I', packet_sn)
    header += device_ip
    header += struct.pack('<Q', timestamp)
    header += struct.pack('<I', car_count)
    
    print(f"包序列号: {packet_sn}")
    print(f"时间戳: {timestamp}")
    print(f"车辆数量: {car_count}")
    
    # 打包车辆数据
    car_data = b''
    for vehicle in vehicles:
        # 车辆ID（8字节）
        car_id_bytes = struct.pack('<I', vehicle['car_id']) + b'\x00\x00\x00\x00'
        
        # 车牌号（16字节）
        car_number = vehicle['car_number'].encode('utf-8')[:16]
        car_number_bytes = car_number.ljust(16, b'\x00')
        
        # 其他字段
        car_type_byte = struct.pack('<B', vehicle['car_type'])
        scope_start = int(vehicle['mileage'] - 50)
        scope_end = int(vehicle['mileage'] + 50)
        scope_bytes = struct.pack('<II', scope_start, scope_end)
        speed_bytes = struct.pack('<f', vehicle['speed'])
        lane_byte = struct.pack('<B', vehicle['lane'])
        mileage_bytes = struct.pack('<I', int(vehicle['mileage']))
        extend1_byte = struct.pack('<B', 0)
        direction_byte = struct.pack('<B', vehicle['direction'])
        
        # 组装单个车辆数据
        single_car_data = (car_id_bytes + car_number_bytes + car_type_byte + 
                          scope_bytes + speed_bytes + lane_byte + 
                          mileage_bytes + extend1_byte + direction_byte)
        
        car_data += single_car_data
        
        print(f"车辆 {vehicle['car_id']}: {vehicle['car_number']}, "
              f"类型: {'小型车' if vehicle['car_type'] == 0 else '大型车'}, "
              f"速度: {vehicle['speed']} m/s, "
              f"车道: {vehicle['lane']}, "
              f"里程: {vehicle['mileage']}, "
              f"方向: {'上行' if vehicle['direction'] == 1 else '下行'}")
    
    return header + car_data

def verify_data(packed_data):
    """验证数据"""
    print("\n=== 验证数据 ===")
    
    print(f"总数据长度: {len(packed_data)} 字节")
    
    # 解析包头
    packet_sn = struct.unpack('<I', packed_data[0:4])[0]
    device_ip = '.'.join(str(b) for b in packed_data[4:8])
    timestamp = struct.unpack('<Q', packed_data[8:16])[0]
    car_count = struct.unpack('<I', packed_data[16:20])[0]
    
    print(f"解析包头:")
    print(f"  包序列号: {packet_sn}")
    print(f"  设备IP: {device_ip}")
    print(f"  时间戳: {timestamp}")
    print(f"  车辆数量: {car_count}")
    
    # 验证数据长度
    expected_length = 20 + car_count * 44
    if len(packed_data) == expected_length:
        print(f"✓ 数据长度正确: {len(packed_data)} 字节")
    else:
        print(f"✗ 数据长度错误: 期望 {expected_length}, 实际 {len(packed_data)}")
    
    return True

def main():
    """主函数"""
    print("=== 车辆轨迹数据格式测试 ===\n")
    
    try:
        # 创建测试数据
        vehicles = create_test_data()
        
        # 打包数据
        packed_data = pack_data(vehicles)
        
        # 验证数据
        verify_data(packed_data)
        
        print("\n=== 测试完成 ===")
        print("✓ 数据格式测试通过")
        print("✓ 可以运行完整的模拟器程序")
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    main()
