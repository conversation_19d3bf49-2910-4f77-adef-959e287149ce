# 车辆轨迹数据模拟器

## 概述

这是一个Python程序，用于模拟高速公路上500辆车的运行轨迹数据，并通过ZMQ发送给现有的Java系统。该模拟器完全兼容现有系统的数据格式和处理流程。

## 功能特性

### 数据规模和范围
- 模拟500辆车的轨迹数据
- 里程范围：1016020米到1176560米（总长度约160.54公里）
- 实时更新车辆位置和状态

### 行驶方向和ZMQ输出
- **上行方向**：从大里程到小里程（1176560→1016020），使用端口9031
- **下行方向**：从小里程到大里程（1016020→1176560），使用端口9030
- 每个方向使用独立的ZMQ发布者

### 车道配置
- 可用车道号：1、2、3、9
- 车道9为应急车道，避免在此车道生成正常行驶车辆
- 主要使用车道1、2、3进行车辆分布
- 支持随机变道模拟真实交通情况

### 车速要求
- 单位：米/秒（m/s）
- 符合中国高速公路实际运行情况：
  - 小型客车：22-33 m/s（80-120 km/h）
  - 大型车辆：17-28 m/s（60-100 km/h）
- 支持动态速度调整

### 车辆类型分布
- 小型车辆（轿车、SUV等）：75%
- 大型车辆（货车、客车等）：25%

## 数据格式

### 二进制数据结构（小端序）

#### 包头（20字节）
- 包序列号：4字节（int）
- 设备IP：4字节（4个byte）
- 时间戳：8字节（long，毫秒）
- 车辆数量：4字节（int）

#### 车辆数据（每辆车44字节）
- 车辆ID：8字节（实际使用4字节，补0）
- 车牌号：16字节（UTF-8编码，不足补0）
- 车辆类型：1字节（0=小型车，1=大型车）
- 影响里程范围：8字节（两个4字节int）
- 车速：4字节（float）
- 车道号：1字节
- 里程：4字节（int）
- 预留字段：1字节
- 方向：1字节（0=下行，1=上行）

## 安装和使用

### 环境要求
- Python 3.7+
- pyzmq库

### 安装依赖
```bash
pip install pyzmq
```

### 运行模拟器
```bash
python vehicle_track_simulator.py
```

### 配置文件
程序支持通过`config.json`文件进行配置，可以调整以下参数：
- 车辆数量
- 速度范围
- 车道配置
- ZMQ端口
- 发送频率

## 与现有系统的兼容性

### 数据格式兼容
- 完全兼容现有的`CarTrackInbound`数据结构
- 二进制数据格式与现有解析逻辑一致
- 字节序和数据类型完全匹配

### ZMQ配置兼容
- 使用与现有系统相同的端口配置
- 支持现有的ZMQ订阅模式
- 数据发送频率符合系统要求

### 业务逻辑兼容
- 车辆类型定义一致（0=小型车，1=大型车）
- 方向定义一致（0=下行，1=上行）
- 车道号定义一致
- 里程计算方式一致

## 监控和调试

### 日志输出
程序提供详细的日志输出，包括：
- 车辆初始化信息
- 数据发送统计
- 错误和警告信息
- 实时统计数据

### 统计信息
每10秒输出一次统计信息：
- 上行/下行车辆数量
- 平均速度
- 车辆类型分布

## 故障排除

### 常见问题

1. **ZMQ连接失败**
   - 检查端口是否被占用
   - 确认防火墙设置
   - 验证网络连接

2. **数据发送失败**
   - 检查ZMQ缓冲区设置
   - 确认接收端是否正常运行
   - 验证数据格式

### 调试命令
```bash
# 检查端口占用
netstat -an | grep 903

# 监控ZMQ连接
ss -tulpn | grep 903
```
