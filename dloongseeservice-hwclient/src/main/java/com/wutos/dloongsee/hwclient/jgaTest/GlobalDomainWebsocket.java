package com.wutos.dloongsee.hwclient.jgaTest;

import cn.hutool.extra.spring.SpringUtil;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import javax.websocket.OnClose;
import javax.websocket.OnError;
import javax.websocket.OnMessage;
import javax.websocket.OnOpen;
import javax.websocket.Session;
import javax.websocket.server.ServerEndpoint;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@ServerEndpoint("/wutos/dloongsee/globalDomain")
public class GlobalDomainWebsocket {

    private static final List<Session> onlineSessionClientList = new CopyOnWriteArrayList<>();

    @Autowired
    @Lazy
    private GlobalDomainController globalDomainController;

    @OnOpen
    public void onOpen(Session session) {
        onlineSessionClientList.add(session);
        globalDomainController.sendGlobalEvents(null);
        log.info("全域信息websocket连接建立成功---------------");
    }

    @OnClose
    public void onClose(Session session) {
        onlineSessionClientList.remove(session);
        log.info("全域信息websocket连接关闭成功---------------");
    }

    @OnError
    public void onError(Session session, Throwable error) {
        log.error("全域信息WebSocket发生错误，错误信息为：{}", error.getMessage());
    }

    @OnMessage
    public void onMessage(String message, Session session) {
        log.info("全域信息websocket收到消息：{}", message);
    }

    public void sendMessage(String message) {
        for (Session session : onlineSessionClientList) {
            session.getAsyncRemote().sendText(message);
        }
    }

}
