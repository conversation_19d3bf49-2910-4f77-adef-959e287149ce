#!/bin/bash

# 车辆轨迹数据模拟器启动脚本

echo "=== 车辆轨迹数据模拟器启动脚本 ==="

# 检查Python版本
python_version=$(python3 --version 2>&1 | awk '{print $2}')
echo "Python版本: $python_version"

# 检查是否安装了pyzmq
if python3 -c "import zmq" 2>/dev/null; then
    echo "✓ pyzmq已安装"
else
    echo "✗ pyzmq未安装，正在安装..."
    pip3 install pyzmq
fi

# 检查端口是否被占用
echo "检查端口占用情况..."
if netstat -an 2>/dev/null | grep -q ":9030"; then
    echo "⚠️  端口9030已被占用"
fi

if netstat -an 2>/dev/null | grep -q ":9031"; then
    echo "⚠️  端口9031已被占用"
fi

# 创建日志目录
mkdir -p logs

# 启动模拟器
echo "启动车辆轨迹数据模拟器..."
python3 vehicle_track_simulator.py 2>&1 | tee logs/simulator_$(date +%Y%m%d_%H%M%S).log
